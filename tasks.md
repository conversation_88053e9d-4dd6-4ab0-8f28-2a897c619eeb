# Chart Text Gap and Font Size Adjustment

## Task: Adjust gap between sales and return values in scrollable chart to match chart with compare, and ensure same text size for sales and returns (main columns only)

### Requirements:
- [ ] Make the gap between sales and return values in scrollable chart match the chart with compare
- [ ] Ensure same text size for sales and returns (only the main columns)
- [ ] Keep compare column styling as is (smaller font sizes)

### Current Issues Found:
1. **Text Gap Inconsistency**: Both regular and comparison charts use the same `textGap` logic but different font sizes
2. **Font Size Inconsistency**: 
   - Regular charts: sales = 12px, returns = 11px
   - Comparison charts: sales = 11px, returns = 10px
3. **Positioning Logic**: Both use `textGap = this.options.isTodayVsPreviousYearsChart ? 6 : 2`

### Implementation Plan:
- [x] Update `drawColumnLabels` function to use consistent font sizes for main columns
- [x] Update `drawComparisonColumnLabels` function to keep smaller font sizes for comparison columns
- [x] Ensure text gap calculation is consistent between both functions
- [ ] Test both scrollable and compare chart types

### Files to Modify:
- `components/charts/snap-charts.js` - Update font sizes and positioning logic
